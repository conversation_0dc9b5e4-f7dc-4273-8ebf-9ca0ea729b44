<script lang="ts">
    import { Button, Input, H1, H2, H3, P1, P2, Dropdown } from '$lib/ui';

    // Form state
    let email = $state();
    let firstName = $state('');
    let lastName = $state('');
    let address = $state('');
    let city = $state('');
    let country = $state('');
    let state = $state('');
    let zip = $state('');
    let cardNumber = $state('');
    let cvc = $state('');
    let expMonth = $state('01');
    let expYear = $state('2024');
    let wantsBump = $state(false);

    // Country options - array of strings for Dropdown component
    const countryOptions = [
        'United States',
        'Canada',
        'United Kingdom',
        'Australia'
    ];

    // State options - array of strings for Dropdown component
    const stateOptions = [
        'California',
        'New York',
        'Texas',
        'Florida'
    ];

    function handleSubmit() {
        console.log('Form submitted');
    }

    function handleBumpToggle() {
        wantsBump = !wantsBump;
    }
</script>

<svelte:head>
	<title>Funnel Hacking Secrets - Special Offer!</title>
</svelte:head>

<div class="webpage">
    <!-- Header Section -->
    <div class="header-section">
        <H1>ClickFunnels Special Offer!</H1>
        <div class="limited-time">
            <H2>Limited Time Only!</H2>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Left Side - Product Info -->
        <div class="product-info">
            {@render productTable()}
            {@render valueStack()}
        </div>

        <!-- Right Side - Order Form -->
        <div class="order-form">
            {@render orderFormContent()}
        </div>
    </div>
</div>

{#snippet productTable()}
    <div class="product-table">
        <H3>Display Your Products</H3>
        <div class="table-container">
            <div class="table-header">
                <span>Item</span>
                <span>Price</span>
            </div>
            <div class="table-row">
                <span>$997 Funnel Hacking Secrets Bundle + 6 Months of ClickFunnels Platinum</span>
                <span>$997</span>
            </div>
            <div class="table-row">
                <span>$97 Secrets Trilogy Box Set</span>
                <span>$97</span>
            </div>
            <div class="table-row">
                <span>$19.95 Domestic Shipping - Box Set</span>
                <span>$19.95</span>
            </div>
            <div class="table-row">
                <span>$29.95 International Shipping - Box Set</span>
                <span>$29.95</span>
            </div>
        </div>
    </div>
{/snippet}

{#snippet valueStack()}
    <div class="value-stack">
        <H2>Here's EVERYTHING You Get INSTANT Access To When You Get 'Funnel Hacking Secrets' Masterclass RIGHT NOW!</H2>

        <div class="value-items">
            <div class="value-item">
                <H3>ClickFunnels: 6 Month Platinum Account</H3>
                <P1>Get your funnels built in as little as 10 minutes, using our SIMPLE "drag n drop" editor!</P1>
                <P2>No tech or coding experience required!</P2>
                <div class="value-price">$1,782 Value</div>
            </div>

            <div class="value-item">
                <H3>Funnel Hacking Secrets Masterclass</H3>
                <P1>Module 1 - Funnel Hacking Secrets</P1>
                <P1>Module 2 - Lead Funnels... Get our Squeeze Page, Survey, Summit, and Brick & Mortar Funnel templates</P1>
                <P1>Module 3 - Unboxing Funnels... Get our Book, Cart, Challenge, Membership, and Supplement Funnel templates!</P1>
                <div class="value-price">$1,997 Value</div>
            </div>

            <div class="value-item">
                <H3>Traffic Secrets Course</H3>
                <P1>Get ALL Of My BEST Traffic-Driving Strategies, Including:</P1>
                <P2>My solo ad secrets… My media buying secrets... My Facebook traffic secrets…</P2>
                <div class="value-price">$1,997 Value</div>
            </div>

            <div class="value-item">
                <H3>Daily Virtual Hack-a-thon</H3>
                <P1>Get your funnel built, in ONE Hackathon session!</P1>
                <P2>Watch and follow along as our experts build all sorts of different funnels step-by-step</P2>
                <div class="value-price">$5,776 Value</div>
            </div>
        </div>

        <div class="total-value">
            <H2>Total Value: $11,552</H2>
            <H1>Get It Today For Only: $997</H1>
            <Button onclick={handleSubmit} fullWidth>Yes! I Want Funnel Hacking Secrets Now!</Button>
        </div>
    </div>
{/snippet}

{#snippet orderFormContent()}
    <div class="form-container">
        <H2>Step #1 Contact Details</H2>
        <div class="form-section">
            <Input
                bind:value={email}
                type="email"
                placeholder="Enter your email address"
                fullWidth
            />
            <div class="form-row">
                <Input
                    bind:value={firstName}
                    placeholder="First Name"
                    fullWidth
                />
                <Input
                    bind:value={lastName}
                    placeholder="Last Name"
                    fullWidth
                />
            </div>
        </div>

        <H2>Step #2 Billing Address</H2>
        <div class="form-section">
            <Input
                bind:value={address}
                placeholder="Full Address"
                fullWidth
            />
            <Input
                bind:value={city}
                placeholder="City Name"
                fullWidth
            />
            <div class="form-row">
                <Dropdown
                    dropdownChoices={countryOptions}
                    bind:dropdownText={country}
                    placeholder="Select Country"
                    fullWidth
                />
                <Dropdown
                    dropdownChoices={stateOptions}
                    bind:dropdownText={state}
                    placeholder="Select State"
                    fullWidth
                />
            </div>
            <Input
                bind:value={zip}
                placeholder="Zip Code"
                fullWidth
            />
        </div>

        <H2>Step #3 Billing Information</H2>
        <div class="form-section">
            <Input
                bind:value={cardNumber}
                placeholder="Credit Card Number"
                fullWidth
            />
            <div class="form-row">
                <Input
                    bind:value={cvc}
                    placeholder="CVC Code"
                    fullWidth
                />
                <div class="expiry-row">
                    <select bind:value={expMonth}>
                        <option value="01">01</option>
                        <option value="02">02</option>
                        <option value="03">03</option>
                        <option value="04">04</option>
                        <option value="05">05</option>
                        <option value="06">06</option>
                        <option value="07">07</option>
                        <option value="08">08</option>
                        <option value="09">09</option>
                        <option value="10">10</option>
                        <option value="11">11</option>
                        <option value="12">12</option>
                    </select>
                    <select bind:value={expYear}>
                        <option value="2024">2024</option>
                        <option value="2025">2025</option>
                        <option value="2026">2026</option>
                        <option value="2027">2027</option>
                        <option value="2028">2028</option>
                        <option value="2029">2029</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Bump Offer -->
        <div class="bump-offer">
            <label class="bump-checkbox">
                <input type="checkbox" bind:checked={wantsBump} onchange={handleBumpToggle} />
                <span class="checkmark"></span>
                <div class="bump-text">
                    <H3>ONE TIME OFFER - $97</H3>
                    <P2>Order The 'Secrets Trilogy' Today And Get The "Unlock The Secrets" Workbook FOR FREE! (Just $97 + shipping... $19.95 US or $29.95 INTL)</P2>
                </div>
            </label>
        </div>

        <Button onclick={handleSubmit} fullWidth>Yes, I will Take It!</Button>

        <div class="guarantee">
            <P2>30-Day Money-Back Guarantee - Peace of Mind!</P2>
        </div>
    </div>
{/snippet}

<style>
    .webpage {
        width: 100%;
        font-size: 1rem;
        -webkit-tap-highlight-color: transparent;
        background: var(--white);
        min-height: 100vh;
    }

    .header-section {
        text-align: center;
        padding: 2rem;
        background: var(--sky-blue);
        border-bottom: 0.25rem solid var(--pitch-black);
    }

    .limited-time {
        margin-top: 1rem;
        padding: 1rem;
        background: var(--yellow);
        border: 0.125rem solid var(--pitch-black);
        border-radius: 0.5rem;
        display: inline-block;
    }

    .main-content {
        display: flex;
        gap: 2rem;
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
    }

    .product-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }

    .order-form {
        flex: 1;
        background: var(--light-aquamarine);
        border: 0.25rem solid var(--pitch-black);
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0.5rem 0.5rem 0 var(--pitch-black);
        height: fit-content;
    }

    .product-table {
        background: var(--white);
        border: 0.25rem solid var(--pitch-black);
        border-radius: 1rem;
        padding: 1.5rem;
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
    }

    .table-container {
        margin-top: 1rem;
    }

    .table-header {
        display: flex;
        justify-content: space-between;
        font-weight: 600;
        padding: 0.75rem;
        background: var(--charcoal);
        color: var(--white);
        border-radius: 0.5rem 0.5rem 0 0;
    }

    .table-row {
        display: flex;
        justify-content: space-between;
        padding: 0.75rem;
        border-bottom: 0.125rem solid var(--pitch-black);
        background: var(--very-light-sky-blue);
    }

    .table-row:last-child {
        border-bottom: none;
        border-radius: 0 0 0.5rem 0.5rem;
    }

    .value-stack {
        background: var(--light-purple);
        border: 0.25rem solid var(--pitch-black);
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
    }

    .value-items {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        margin: 2rem 0;
    }

    .value-item {
        background: var(--white);
        border: 0.125rem solid var(--pitch-black);
        border-radius: 0.75rem;
        padding: 1.5rem;
        box-shadow: 0.125rem 0.125rem 0 var(--pitch-black);
    }

    .value-price {
        font-weight: 600;
        color: var(--purple);
        font-size: 1.125rem;
        margin-top: 0.5rem;
    }

    .total-value {
        text-align: center;
        background: var(--yellow);
        border: 0.25rem solid var(--pitch-black);
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
    }

    .form-container {
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }

    .form-section {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .form-row {
        display: flex;
        gap: 1rem;
    }

    .expiry-row {
        display: flex;
        gap: 0.5rem;
        flex: 1;
    }

    .expiry-row select {
        flex: 1;
        padding: 0.75rem;
        border: 0.125rem solid var(--pitch-black);
        border-radius: 0.5rem;
        background: var(--white);
        font-family: 'Open Sans', sans-serif;
        font-weight: 600;
    }

    .bump-offer {
        background: var(--light-yellow);
        border: 0.25rem solid var(--pitch-black);
        border-radius: 0.75rem;
        padding: 1.5rem;
        box-shadow: 0.125rem 0.125rem 0 var(--pitch-black);
    }

    .bump-checkbox {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        cursor: pointer;
    }

    .bump-checkbox input[type="checkbox"] {
        width: 1.25rem;
        height: 1.25rem;
        margin: 0;
    }

    .bump-text {
        flex: 1;
    }

    .guarantee {
        text-align: center;
        margin-top: 1rem;
        padding: 1rem;
        background: var(--light-aquamarine);
        border-radius: 0.5rem;
    }

    @media (max-width: 768px) {
        .main-content {
            flex-direction: column;
            padding: 1rem;
        }

        .form-row {
            flex-direction: column;
        }

        .header-section {
            padding: 1rem;
        }
    }
</style>